import { ArrowCircleRight2, CloseCircle, Tag2 } from 'iconsax-react';
import { Icon } from '../../../components/icons/icon';
import phone from '../../../assets/images/phone.png';
import { Success } from './success';
import { useState } from 'react';
import { useGiftItems } from '../../../lib/contexts/GiftItemsContext';

interface GiftItem {
  id: number;
  name: string;
  description?: string;
  price?: number | string;
  image: string;
  quantity?: number;
  link?: string;
}

interface RegistryData {
  registryTitle?: string;
  giftTypes?: string[];
  giftItems?: GiftItem[];
}

interface PreviewAndCreateProps {
  initialData?: RegistryData;
  onClose?: () => void;
}

export const PreviewAndCreate = ({
  initialData = {},
  onClose = () => {},
}: PreviewAndCreateProps) => {
  const [open, setOpen] = useState(false);
  const { giftItems, removeGiftItem, getItemCount } = useGiftItems();

  // Default items if no context items are available
  const defaultItems: GiftItem[] = [
    {
      id: 1,
      name: 'iPhone 15 Pro',
      description: 'Flawless makeup for your big day.',
      price: 150000,
      image: phone,
      link: 'https://apple.com/iphone-15-pro',
    },
    {
      id: 2,
      name: 'iPhone 15 Pro',
      description: 'Flawless makeup for your big day.',
      price: 150000,
      image: phone,
      link: 'https://apple.com/iphone-15-pro',
    },
    {
      id: 3,
      name: 'iPhone 15 Pro',
      description: 'Flawless makeup for your big day.',
      price: 150000,
      image: phone,
      link: 'https://apple.com/iphone-15-pro',
    },
  ];

  // Use context items first, then initialData.giftItems, then default items
  const items =
    giftItems.length > 0
      ? giftItems
      : initialData.giftItems?.length
      ? initialData.giftItems
      : defaultItems;

  const accountDetails = {
    bank: 'GTBank',
    accountNumber: '**********',
    accountName: 'ADE BOLUWATIFE',
    location: 'Lekki Conservation Center, Lekki, Lagos State',
  };

  //   const removeItem = (id) => {
  //     setItems(items.filter((item) => item.id !== id));
  //   };

  return (
    <>
      <div className="bg-white min-h-screen mb-40 px-4 lg:px-0 lg:ml-54">
        <div className="flex justify-center flex-col lg:flex-row items-center lg:items-start mt-7 ">
          <div className="max-w-[560px] w-full ">
            <h1 className="text-[28px] font-semibold italic mb-4 ">
              {initialData.registryTitle || "Oladele's birthday gifts"}
            </h1>

            <div className=" pt-5 px-4 pb-4 rounded-xl bg-[linear-gradient(182.72deg,#FEF7F4_20.31%,#F5F6FE_97.2%)]">
              <div className="text-2xl font-bold mb-1.5">**********</div>
              <div>
                <span className="text-cus-orange-700 font-medium text-sm">
                  {accountDetails.bank}{' '}
                </span>
                <span className="text-grey-950 text-sm italic font-bold">
                  • {accountDetails.accountName}
                </span>
              </div>
              <div className="flex items-center text-sm italic text-dark-blue-200 gap-2 mt-4.5 ">
                <Icon name="bus" />
                <span>{accountDetails.location}</span>
              </div>
            </div>

            <div className="my-6 text-primary text-sm bg-primary-250 w-fit px-2.5 py-1 rounded-2xl italic font-bold">
              {getItemCount()} Gift Items
            </div>

            <div className="space-y-6">
              {items.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center py-3 sm:py-0 flex-col md:flex-row border gap-4 border-grey-150 rounded-[14px]">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-[155px] h-full object-contain lg:rounded-l-[14px]"
                  />
                  <div className="flex-1 pr-2">
                    <div className="flex justify-between">
                      <h2 className="text-[22px] text-grey-750 font-medium">
                        {item.name}
                      </h2>
                      <button
                        onClick={() => removeGiftItem(item.id)}
                        className="cursor-pointer hover:opacity-70 transition-opacity">
                        <CloseCircle size={28} variant="Bulk" color="#9499F7" />
                      </button>
                    </div>
                    {item.description && (
                      <p className="text-grey-100 my-1">{item.description}</p>
                    )}
                    {item.link && (
                      <div className="">
                        <span className="underline text-primary text-xs italic font-bold">
                          Link to Item
                        </span>
                      </div>
                    )}
                    <div className="mt-4 flex items-center gap-1.5 bg-light-blue-150 w-fit py-1.5 px-2.5 rounded-2xl">
                      <Tag2 size={12} variant="Bulk" color="#5925DC " />
                      <span className="text-perple text-sm font-medium">
                        ₦
                        {item.price
                          ? typeof item.price === 'string'
                            ? parseFloat(
                                item.price.replace(/,/g, '')
                              ).toLocaleString()
                            : Number(item.price).toLocaleString()
                          : '0'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <button
            type="button"
            onClick={() => setOpen(true)}
            className="bg-primary text-base mt-5 lg:mt-0 font-semibold cursor-pointer text-white rounded-full py-2.5 px-4 flex items-center gap-2">
            Create Gift Registry
            <ArrowCircleRight2 variant="Bulk" color="#fff" size={20} />
          </button>
        </div>
      </div>
      {open && <Success onClose={onClose} />}
    </>
  );
};
